---
name: Bug report
about: Create a report to help us improve
title: ''
labels: ''
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:


**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Desktop (please complete the following information):**
 - OS: [e.g. Linux Mint]
 - Terminal [e.g. bash, WSL, cygwin]
 - Python Version [e.g. 3.5, 3.8]

**Smartphone (please complete the following information):**
 - Device: [e.g. iPhone6]
 - OS: [e.g. iOS8.1]
 - Terminal [e.g. iSH, Termux]
 - Python Version [e.g. 3.5, 3.8]

**Additional context**
Add any other context about the problem here.
